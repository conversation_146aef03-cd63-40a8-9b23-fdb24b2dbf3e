/// دليل الروابط الخارجية المجانية لملفات PDF
class ExternalLinksGuide {
  
  /// قائمة الخدمات المجانية المدعومة
  static const List<Map<String, String>> freeServices = [
    {
      'name': 'Google Drive',
      'description': 'مجاني مع 15 GB مساحة تخزين',
      'steps': '''
1. ارفع ملف PDF إلى Google Drive
2. انقر بزر الماوس الأيمن على الملف
3. اختر "مشاركة" → "الحصول على رابط"
4. غير الإعداد إلى "يمكن لأي شخص لديه الرابط العرض"
5. انسخ الرابط وغير صيغته:
   من: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
   إلى: https://drive.google.com/uc?id=FILE_ID''',
      'example': 'https://drive.google.com/uc?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
    },
    {
      'name': 'Dropbox',
      'description': 'مجاني مع 2 GB مساحة تخزين',
      'steps': '''
1. ارفع ملف PDF إلى Dropbox
2. انقر بزر الماوس الأيمن على الملف
3. اختر "مشاركة" → "إنشاء رابط"
4. انسخ الرابط وغير النهاية:
   من: https://www.dropbox.com/s/abc123/file.pdf?dl=0
   إلى: https://www.dropbox.com/s/abc123/file.pdf?dl=1''',
      'example': 'https://www.dropbox.com/s/abc123/sample.pdf?dl=1',
    },
    {
      'name': 'OneDrive',
      'description': 'مجاني مع 5 GB مساحة تخزين',
      'steps': '''
1. ارفع ملف PDF إلى OneDrive
2. انقر بزر الماوس الأيمن على الملف
3. اختر "مشاركة" → "نسخ الرابط"
4. غير "view" إلى "download" في الرابط:
   من: https://1drv.ms/b/s!ABC123/file.pdf?e=view
   إلى: https://1drv.ms/b/s!ABC123/file.pdf?e=download''',
      'example': 'https://1drv.ms/b/s!ABC123/sample.pdf?e=download',
    },
    {
      'name': 'GitHub',
      'description': 'مجاني تماماً للملفات العامة',
      'steps': '''
1. أنشئ مستودع عام في GitHub
2. ارفع ملف PDF إلى المستودع
3. انقر على الملف في GitHub
4. اضغط على "Raw" للحصول على الرابط المباشر''',
      'example': 'https://raw.githubusercontent.com/username/repo/main/file.pdf',
    },
    {
      'name': 'MediaFire',
      'description': 'مجاني مع 10 GB مساحة تخزين',
      'steps': '''
1. ارفع ملف PDF إلى MediaFire
2. انقر على "مشاركة الملف"
3. انسخ "الرابط المباشر" وليس رابط الصفحة''',
      'example': 'https://www.mediafire.com/file/abc123/file.pdf/file',
    },
  ];

  /// نصائح مهمة للروابط
  static const List<String> importantTips = [
    '✅ تأكد أن الرابط ينتهي بـ .pdf',
    '✅ اختبر الرابط في المتصفح قبل إضافته',
    '✅ تأكد أن الملف متاح للعرض العام',
    '⚠️ تجنب الروابط المؤقتة أو المحدودة الوقت',
    '⚠️ لا تستخدم روابط تحتاج تسجيل دخول',
    '🔒 احرص على عدم مشاركة ملفات حساسة',
  ];

  /// أمثلة روابط تجريبية للاختبار
  static const List<Map<String, String>> testLinks = [
    {
      'name': 'ملف PDF تجريبي - W3C',
      'url': 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      'description': 'ملف تجريبي من W3C للاختبار',
    },
    {
      'name': 'دليل PDF - Mozilla',
      'url': 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      'description': 'ملف تجريبي من Mozilla PDF.js',
    },
  ];

  /// التحقق من صحة رابط PDF
  static bool isValidPDFUrl(String url) {
    if (url.isEmpty) return false;
    
    // التحقق من أن الرابط يبدأ بـ http أو https
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }
    
    // التحقق من أن الرابط ينتهي بـ .pdf أو يحتوي على معاملات PDF
    if (url.toLowerCase().endsWith('.pdf') ||
        url.contains('pdf') ||
        url.contains('drive.google.com/uc') ||
        url.contains('dropbox.com') ||
        url.contains('onedrive') ||
        url.contains('mediafire.com')) {
      return true;
    }
    
    return false;
  }

  /// الحصول على اسم الخدمة من الرابط
  static String getServiceName(String url) {
    if (url.contains('drive.google.com')) return 'Google Drive';
    if (url.contains('dropbox.com')) return 'Dropbox';
    if (url.contains('1drv.ms') || url.contains('onedrive')) return 'OneDrive';
    if (url.contains('github.com') || url.contains('githubusercontent.com')) return 'GitHub';
    if (url.contains('mediafire.com')) return 'MediaFire';
    return 'رابط مباشر';
  }
}
