rules_version = '2';

// قواعد Firebase Storage للتطبيق التعليمي
service firebase.storage {
  match /b/{bucket}/o {
    
    // قواعد ملفات PDF
    match /pdfs/{allPaths=**} {
      // السماح بالقراءة للجميع (المستخدمين المسجلين)
      allow read: if request.auth != null;
      
      // السماح بالكتابة للأدمن فقط
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
      
      // السماح بالحذف للأدمن فقط
      allow delete: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    // قواعد ملفات الصور للمنشورات
    match /posts/{allPaths=**} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // السماح بالكتابة للمستخدمين المسجلين
      allow write: if request.auth != null;
      
      // السماح بالحذف لصاحب الملف أو الأدمن
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.metadata.uploadedBy ||
         request.auth.token.email == '<EMAIL>');
    }
    
    // قواعد ملفات الصور للدردشة
    match /chat_images/{allPaths=**} {
      // السماح بالقراءة للمستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح بالكتابة للمستخدمين المسجلين
      allow write: if request.auth != null;
      
      // السماح بالحذف لصاحب الملف أو الأدمن
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.metadata.uploadedBy ||
         request.auth.token.email == '<EMAIL>');
    }
    
    // قواعد ملفات الصور الشخصية
    match /profile_images/{userId}/{allPaths=**} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // السماح بالكتابة لصاحب الملف أو الأدمن
      allow write: if request.auth != null && 
        (request.auth.uid == userId ||
         request.auth.token.email == '<EMAIL>');
      
      // السماح بالحذف لصاحب الملف أو الأدمن
      allow delete: if request.auth != null && 
        (request.auth.uid == userId ||
         request.auth.token.email == '<EMAIL>');
    }
    
    // قواعد عامة للملفات المؤقتة
    match /temp/{allPaths=**} {
      // السماح بالقراءة والكتابة للمستخدمين المسجلين
      allow read, write: if request.auth != null;
      
      // السماح بالحذف للجميع (ملفات مؤقتة)
      allow delete: if request.auth != null;
    }
    
    // منع الوصول لأي ملفات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
