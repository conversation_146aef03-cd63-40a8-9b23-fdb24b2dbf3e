import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// أداة اختبار روابط PDF لتشخيص المشاكل
class PDFUrlTester {
  /// اختبار رابط PDF محدد
  static Future<Map<String, dynamic>> testPdfUrl(String url) async {
    final result = <String, dynamic>{
      'originalUrl': url,
      'success': false,
      'error': null,
      'statusCode': null,
      'contentType': null,
      'contentLength': null,
      'directUrl': null,
      'isPdf': false,
      'details': [],
    };

    try {
      if (kDebugMode) {
        print('🧪 بدء اختبار الرابط: $url');
      }

      // تحويل الرابط إلى رابط مباشر
      final directUrl = _convertToDirectDownloadUrl(url);
      result['directUrl'] = directUrl;

      if (kDebugMode) {
        print('🔗 الرابط المحول: $directUrl');
      }

      // إرسال طلب HEAD أولاً للحصول على معلومات الملف
      final headResponse = await http.head(Uri.parse(directUrl)).timeout(
        const Duration(seconds: 30),
      );

      result['statusCode'] = headResponse.statusCode;
      result['contentType'] = headResponse.headers['content-type'];
      result['contentLength'] = headResponse.headers['content-length'];

      if (kDebugMode) {
        print('📊 نتائج HEAD request:');
        print('   Status: ${headResponse.statusCode}');
        print('   Content-Type: ${headResponse.headers['content-type']}');
        print('   Content-Length: ${headResponse.headers['content-length']}');
        print('   Headers: ${headResponse.headers}');
      }

      if (headResponse.statusCode == 200) {
        // التحقق من نوع المحتوى
        final contentType = headResponse.headers['content-type'] ?? '';
        result['isPdf'] = contentType.contains('pdf');

        if (result['isPdf']) {
          result['success'] = true;
          result['details'].add('✅ الملف هو PDF صحيح');
        } else {
          result['details'].add('⚠️ نوع المحتوى ليس PDF: $contentType');
          
          // جرب تحميل جزء صغير للتحقق
          final partialResponse = await http.get(
            Uri.parse(directUrl),
            headers: {'Range': 'bytes=0-1023'}, // أول 1KB
          ).timeout(const Duration(seconds: 30));

          if (partialResponse.statusCode == 206 || partialResponse.statusCode == 200) {
            final bytes = partialResponse.bodyBytes;
            if (bytes.length >= 4 && 
                bytes[0] == 0x25 && bytes[1] == 0x50 && 
                bytes[2] == 0x44 && bytes[3] == 0x46) {
              result['isPdf'] = true;
              result['success'] = true;
              result['details'].add('✅ تم التحقق من PDF signature');
            } else {
              result['details'].add('❌ الملف لا يحتوي على PDF signature');
              result['details'].add('📄 أول 20 بايت: ${bytes.take(20).toList()}');
            }
          }
        }
      } else if (headResponse.statusCode == 302 || headResponse.statusCode == 301) {
        result['details'].add('🔄 إعادة توجيه: ${headResponse.headers['location']}');
        
        // جرب الرابط الجديد
        final newUrl = headResponse.headers['location'];
        if (newUrl != null) {
          final redirectResult = await testPdfUrl(newUrl);
          result.addAll(redirectResult);
        }
      } else {
        result['error'] = 'HTTP ${headResponse.statusCode}: ${headResponse.reasonPhrase}';
        result['details'].add('❌ خطأ HTTP: ${headResponse.statusCode}');
      }

    } catch (e) {
      result['error'] = e.toString();
      result['details'].add('❌ خطأ في الاتصال: $e');
      
      if (kDebugMode) {
        print('❌ خطأ في اختبار الرابط: $e');
      }
    }

    return result;
  }

  /// تحويل روابط Google Drive إلى روابط تحميل مباشرة
  static String _convertToDirectDownloadUrl(String url) {
    if (kDebugMode) {
      print('🔗 معالجة الرابط: $url');
    }

    // إذا كان رابط Google Drive
    if (url.contains('drive.google.com')) {
      // نمط 1: /file/d/FILE_ID/view أو /file/d/FILE_ID/edit
      RegExp regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
      RegExpMatch? match = regex.firstMatch(url);

      if (match != null) {
        final fileId = match.group(1);
        final directUrl = 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive: $fileId -> $directUrl');
        }
        return directUrl;
      }

      // نمط 2: إذا كان يحتوي على id= بالفعل
      regex = RegExp(r'[?&]id=([a-zA-Z0-9-_]+)');
      match = regex.firstMatch(url);
      if (match != null) {
        final fileId = match.group(1);
        final directUrl = 'https://drive.google.com/uc?export=download&id=$fileId&confirm=t';
        if (kDebugMode) {
          print('🔄 تحويل Google Drive (ID): $fileId -> $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط Dropbox
    if (url.contains('dropbox.com') && url.contains('dl=0')) {
      final directUrl = url.replaceAll('dl=0', 'dl=1');
      if (kDebugMode) {
        print('🔄 تحويل Dropbox: $directUrl');
      }
      return directUrl;
    }

    // إذا كان رابط OneDrive
    if (url.contains('1drv.ms') || url.contains('onedrive.live.com')) {
      if (url.contains('?')) {
        final directUrl = '$url&download=1';
        if (kDebugMode) {
          print('🔄 تحويل OneDrive: $directUrl');
        }
        return directUrl;
      }
    }

    // إذا كان رابط مباشر، نعيده كما هو
    if (kDebugMode) {
      print('✅ رابط مباشر: $url');
    }
    return url;
  }

  /// اختبار الرابط المحدد من المستخدم
  static Future<void> testUserUrl() async {
    const testUrl = 'https://drive.google.com/file/d/1zFo-rWJKVYOZmC0wJdWzy5VR97X2Dpvy/view';
    
    if (kDebugMode) {
      print('🧪 اختبار رابط المستخدم: $testUrl');
      print('=' * 50);
    }

    final result = await testPdfUrl(testUrl);

    if (kDebugMode) {
      print('📊 نتائج الاختبار:');
      print('   الرابط الأصلي: ${result['originalUrl']}');
      print('   الرابط المباشر: ${result['directUrl']}');
      print('   نجح: ${result['success']}');
      print('   كود الحالة: ${result['statusCode']}');
      print('   نوع المحتوى: ${result['contentType']}');
      print('   حجم المحتوى: ${result['contentLength']}');
      print('   هو PDF: ${result['isPdf']}');
      print('   خطأ: ${result['error']}');
      print('   التفاصيل:');
      for (final detail in result['details']) {
        print('     $detail');
      }
      print('=' * 50);
    }
  }
}
