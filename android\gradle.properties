org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Kotlin daemon settings
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G
kotlin.incremental=false
kotlin.compiler.execution.strategy=in-process

# Gradle performance
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
