import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/pdf_model.dart';

/// خدمة إضافة بيانات تجريبية للاختبار
class TestDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة ملفات PDF تجريبية للاختبار
  static Future<void> addTestPDFs() async {
    if (!kDebugMode) return; // فقط في وضع التطوير

    try {
      print('🔄 بدء إضافة ملفات PDF تجريبية...');

      final testPDFs = [
        // ملفات للفرقة الأولى - الترم الأول
        PDFModel(
          id: 'test_fiqh_issues_questions',
          name: 'أسئلة قضايا فقهية معاصرة',
          url:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          category: 'أسئلة',
          subjectId: 'fiqh_issues',
          subjectName: 'قضايا فقهية معاصرة',
          yearId: 'year1',
          semesterId: 'year1_sem1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uploadedBy: '<EMAIL>',
          uploaderName: 'أدمن النظام',
          fileSize: 2.5,
          fileName: 'fiqh_issues_questions.pdf',
          fileExtension: 'pdf',
          isFromUrl: true,
          originalUrl:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        ),

        PDFModel(
          id: 'test_fiqh_issues_summary',
          name: 'ملخص قضايا فقهية معاصرة',
          url:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          category: 'ملخصات',
          subjectId: 'fiqh_issues',
          subjectName: 'قضايا فقهية معاصرة',
          yearId: 'year1',
          semesterId: 'year1_sem1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uploadedBy: '<EMAIL>',
          uploaderName: 'أدمن النظام',
          fileSize: 1.8,
          fileName: 'fiqh_issues_summary.pdf',
          fileExtension: 'pdf',
          isFromUrl: true,
          originalUrl:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        ),

        // ملفات للقرآن الكريم
        PDFModel(
          id: 'test_quran_famous_places',
          name: 'أشهر المواضع في القرآن الكريم',
          url:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          category: 'أشهر المواضع',
          subjectId: 'quran',
          subjectName: 'القرآن الكريم',
          yearId: 'year1',
          semesterId: 'year1_sem2',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uploadedBy: '<EMAIL>',
          uploaderName: 'أدمن النظام',
          fileSize: 3.2,
          fileName: 'quran_famous_places.pdf',
          fileExtension: 'pdf',
          isFromUrl: true,
          originalUrl:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        ),

        PDFModel(
          id: 'test_quran_exams',
          name: 'امتحانات القرآن الكريم',
          url:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          category: 'الامتحانات',
          subjectId: 'quran',
          subjectName: 'القرآن الكريم',
          yearId: 'year1',
          semesterId: 'year1_sem2',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uploadedBy: '<EMAIL>',
          uploaderName: 'أدمن النظام',
          fileSize: 2.1,
          fileName: 'quran_exams.pdf',
          fileExtension: 'pdf',
          isFromUrl: true,
          originalUrl:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        ),

        // ملفات للقانون المدني
        PDFModel(
          id: 'test_civil_law_book',
          name: 'الكتاب الرسمي للقانون المدني',
          url:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          category: 'الكتاب الرسمي',
          subjectId: 'civil_law',
          subjectName: 'القانون المدني',
          yearId: 'year2',
          semesterId: 'year2_sem2',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          uploadedBy: '<EMAIL>',
          uploaderName: 'أدمن النظام',
          fileSize: 5.7,
          fileName: 'civil_law_official_book.pdf',
          fileExtension: 'pdf',
          isFromUrl: true,
          originalUrl:
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        ),
      ];

      // إضافة الملفات إلى Firestore
      for (final pdf in testPDFs) {
        await _firestore.collection('pdfs').doc(pdf.id).set(pdf.toJson());
        print('✅ تم إضافة: ${pdf.name}');
      }

      print('🎉 تم إضافة جميع ملفات PDF التجريبية بنجاح!');
    } catch (e) {
      print('❌ خطأ في إضافة ملفات PDF التجريبية: $e');
    }
  }

  /// حذف جميع ملفات PDF التجريبية
  static Future<void> removeTestPDFs() async {
    if (!kDebugMode) return;

    try {
      print('🔄 بدء حذف ملفات PDF التجريبية...');

      final testIds = [
        'test_fiqh_issues_questions',
        'test_fiqh_issues_summary',
        'test_quran_famous_places',
        'test_quran_exams',
        'test_civil_law_book',
      ];

      for (final id in testIds) {
        await _firestore.collection('pdfs').doc(id).delete();
        print('🗑️ تم حذف: $id');
      }

      print('🎉 تم حذف جميع ملفات PDF التجريبية!');
    } catch (e) {
      print('❌ خطأ في حذف ملفات PDF التجريبية: $e');
    }
  }
}
