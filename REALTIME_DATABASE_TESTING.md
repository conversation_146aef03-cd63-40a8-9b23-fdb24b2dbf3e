# 🧪 دليل اختبار Realtime Database للـ PDF

## 🎯 **الهدف:**
اختبار النظام الجديد للتأكد من أن Realtime Database يعمل بشكل صحيح لإدارة ملفات PDF.

## 📋 **خطوات الاختبار:**

### **الخطوة 1: التحضير**
1. تأكد من أن Firebase Realtime Database مفعل في المشروع
2. شغل التطبيق
3. سجل دخول بحساب الأدمن (`<EMAIL>`)

### **الخطوة 2: اختبار إضافة PDF**
1. اذهب لأي مادة (مثل: القانون المدني)
2. اضغط زر الأدمن (الزر الأزرق في الأسفل)
3. اختر **"إضافة ملف PDF"**
4. أدخل البيانات:
   - **الاسم:** اختبار Realtime Database
   - **الرابط:** https://www.google.com
5. اضغط **"إضافة"**
6. انتظر رسالة النجاح

### **الخطوة 3: التحقق من النتائج**

#### **في التطبيق:**
- [ ] ظهور رسالة "تم إضافة الملف بنجاح"
- [ ] عدم ظهور أخطاء في Console
- [ ] سرعة في الاستجابة

#### **في Firebase Console:**
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `legal2025`
3. اذهب إلى **Realtime Database**
4. تحقق من وجود البيانات في المسار:
   ```
   pdfs > [subject_id] > [category] > [pdf_id]
   ```

### **الخطوة 4: اختبار عرض PDF**
1. أعد فتح نفس المادة والفئة
2. تحقق من ظهور الملف المضاف
3. جرب النقر على الملف للتأكد من فتحه

## 🔍 **ما تبحث عنه:**

### **علامات النجاح:**
- ✅ إضافة سريعة (أقل من 3 ثواني)
- ✅ رسالة نجاح واضحة
- ✅ ظهور البيانات في Realtime Database
- ✅ عدم وجود أخطاء في Console

### **علامات المشاكل:**
- ❌ رسائل خطأ
- ❌ بطء في الاستجابة
- ❌ عدم ظهور البيانات في Firebase
- ❌ أخطاء في Console

## 🛠️ **حل المشاكل:**

### **إذا فشلت الإضافة:**
1. تحقق من أن Realtime Database مفعل
2. تحقق من قواعد Database:
   ```json
   {
     "rules": {
       ".read": true,
       ".write": true
     }
   }
   ```
3. تحقق من الاتصال بالإنترنت

### **إذا لم تظهر البيانات:**
1. تحقق من Firebase Console > Realtime Database
2. ابحث عن المسار: `pdfs/[subject_id]/[category]`
3. تحقق من أن `isActive: true`

## 📊 **هيكل البيانات المتوقع:**

```json
{
  "pdfs": {
    "civil_law": {
      "أسئلة": {
        "-NxxxxxxxxxxxXXX": {
          "id": "-NxxxxxxxxxxxXXX",
          "name": "اختبار Realtime Database",
          "url": "https://www.google.com",
          "category": "أسئلة",
          "subjectId": "civil_law",
          "createdAt": 1704067200000,
          "uploadedBy": "<EMAIL>",
          "isActive": true
        }
      }
    }
  }
}
```

## 🔧 **اختبارات إضافية:**

### **اختبار 1: ملفات متعددة**
- أضف 3 ملفات مختلفة
- تحقق من ترتيبها (الأحدث أولاً)
- تحقق من عدم تداخل البيانات

### **اختبار 2: فئات مختلفة**
- أضف ملفات في فئات مختلفة (أسئلة، امتحانات، ملخصات)
- تحقق من أن كل فئة منفصلة

### **اختبار 3: مواد مختلفة**
- أضف ملفات في مواد مختلفة
- تحقق من عدم تداخل البيانات بين المواد

## 📱 **اختبار الأداء:**

### **السرعة:**
- إضافة PDF: يجب أن تكون أقل من 3 ثواني
- عرض القائمة: يجب أن يكون فورياً
- فتح الملف: يجب أن يكون سريعاً

### **الاستقرار:**
- لا أخطاء في Console
- لا تجمد في الواجهة
- استجابة سلسة

## 🎯 **النتائج المتوقعة:**

### **مقارنة مع النظام القديم:**
- **أسرع في الإضافة** (Realtime Database أسرع للبيانات البسيطة)
- **أقل تعقيد** (معاملات أقل مطلوبة)
- **أقل مشاكل** (قواعد أبسط)

### **الفوائد الملاحظة:**
1. **سرعة الاستجابة** - إضافة فورية تقريباً
2. **بساطة الكود** - أقل معاملات مطلوبة
3. **استقرار أكبر** - أقل نقاط فشل

## 📞 **في حالة المشاكل:**

### **خطوات التشخيص:**
1. تحقق من Console المتصفح (F12)
2. تحقق من Firebase Console > Usage
3. تحقق من قواعد Realtime Database
4. تحقق من الاتصال بالإنترنت

### **معلومات مفيدة:**
- **Project ID:** legal2025
- **Database URL:** https://legal2025-default-rtdb.firebaseio.com/
- **Admin Email:** <EMAIL>

---

**ملاحظة:** هذا الاختبار يركز على وظيفة إضافة PDF فقط. باقي الوظائف (حذف، تحديث) ستحتاج اختبارات منفصلة لاحقاً.
